import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { join } from 'path';
import { EmailService } from './email.service.js';

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        transport: {
          host: configService.get('SMTP_HOST'),
          port: parseInt(configService.get('SMTP_PORT', '587'), 10),
          secure: configService.get('SMTP_SECURE') === 'true',
          auth: {
            user: configService.get('SMTP_USER'),
            pass: configService.get('SMTP_PASSWORD'),
          },
          // Additional options for development
          ignoreTLS: configService.get('NODE_ENV') === 'development',
          requireTLS: configService.get('NODE_ENV') === 'production',
        },
        defaults: {
          from: `"${configService.get('EMAIL_FROM_NAME', 'RSGlider')}" <${configService.get('SMTP_FROM', '<EMAIL>')}>`,
        },
        template: {
          dir: join(__dirname, 'templates'),
          adapter: new HandlebarsAdapter({
            // Handlebars helpers
            helpers: {
              // Format date helper
              formatDate: function (date: any) {
                return new Intl.DateTimeFormat('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                }).format(new Date(date));
              },
              // Conditional helper
              ifEquals: function (arg1: any, arg2: any, options: any) {
                return (arg1 === arg2) ? options.fn(this) : options.inverse(this);
              },
              // URL helper for generating links
              url: function (path: any) {
                const baseUrl = process.env.EMAIL_BASE_URL || 'http://localhost:3000';
                return `${baseUrl}${path}`;
              },
            } as any,
          }),
          options: {
            strict: true,
          },
        },
        // Preview emails in development
        preview: configService.get('NODE_ENV') === 'development',
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [EmailService],
  exports: [EmailService],
})
export class EmailModule { }
