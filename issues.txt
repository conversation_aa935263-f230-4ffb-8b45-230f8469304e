pnpm run test:cov

> rsglider-api@1.0.0 test:cov /app
> jest --coverage --silent

 PASS  src/auth/auth.service.integration.spec.ts (12.955 s)
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RoutesResolver] DeveloperController {/developers}:
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RouterExplorer] Mapped {/developers/create, POST} route +2ms
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RouterExplorer] Mapped {/developers/profile, GET} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RouterExplorer] Mapped {/developers/provision, POST} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RouterExplorer] Mapped {/developers/sync, POST} route +1ms
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RouterExplorer] Mapped {/developers/repositories, GET} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RouterExplorer] Mapped {/developers/repositories/sync, POST} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [RouterExplorer] Mapped {/developers/repositories/:id, GET} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:00 PM     LOG [NestApplication] Nest application successfully started +1ms
 PASS  src/developer/developer.controller.integration.spec.ts (8.525 s)
 PASS  src/common/services/developer-management.service.integration.spec.ts (5.83 s)
 PASS  src/common/services/file-uploads.service.integration.spec.ts (5.32 s)
 PASS  src/common/services/repository-sync.service.integration.spec.ts (5.108 s)
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [RoutesResolver] UploadsController {/uploads}:
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [RouterExplorer] Mapped {/uploads/direct, POST} route +4ms
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [RouterExplorer] Mapped {/uploads/presigned, POST} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [RouterExplorer] Mapped {/uploads/:fileId/complete, POST} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [RouterExplorer] Mapped {/uploads/my-files, GET} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [RouterExplorer] Mapped {/uploads/:fileId/download, GET} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [RouterExplorer] Mapped {/uploads/:fileId, DELETE} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:25 PM     LOG [NestApplication] Nest application successfully started +1ms
 PASS  src/uploads/uploads.controller.integration.spec.ts
 PASS  src/file-uploads/file-uploads.service.integration.spec.ts
[Nest] 10709  - 05/31/2025, 10:42:31 PM     LOG [RoutesResolver] WebhooksController {/webhooks}:
[Nest] 10709  - 05/31/2025, 10:42:31 PM     LOG [RouterExplorer] Mapped {/webhooks/gitea, POST} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:31 PM     LOG [RouterExplorer] Mapped {/webhooks/gitea/test, POST} route +1ms
[Nest] 10709  - 05/31/2025, 10:42:31 PM     LOG [NestApplication] Nest application successfully started +0ms
 PASS  src/webhooks/webhooks.controller.integration.spec.ts
[Nest] 10709  - 05/31/2025, 10:42:33 PM     LOG [RoutesResolver] ClientController {/client}:
[Nest] 10709  - 05/31/2025, 10:42:33 PM     LOG [RouterExplorer] Mapped {/client/check_update/:target/:arch/:current_version, GET} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:33 PM     LOG [RouterExplorer] Mapped {/client/check_update, GET} route +0ms
[Nest] 10709  - 05/31/2025, 10:42:33 PM     LOG [NestApplication] Nest application successfully started +1ms
 PASS  src/client/client.controller.integration.spec.ts
 PASS  src/common/services/client-releases.service.integration.spec.ts
 PASS  src/auth/auth.module.spec.ts
 PASS  src/common/services/services.module.spec.ts
 PASS  src/database/database.module.spec.ts
 PASS  src/common/utils/secure-logger.spec.ts
 PASS  src/common/services/gitea.service.spec.ts
 PASS  src/users/dto/users-dtos.spec.ts
 PASS  src/common/services/webhook-processor.service.spec.ts
 PASS  src/app.module.spec.ts
 PASS  src/common/services/redis.service.spec.ts
 PASS  src/common/services/index.spec.ts
 PASS  src/common/dto/common-dtos.spec.ts
 PASS  src/common/services/client-releases.service.spec.ts
 PASS  src/developer/dto/developer-dtos.spec.ts
 PASS  src/developer/developer.controller.spec.ts
 PASS  src/store/dto/store-dtos.spec.ts
 PASS  src/common/services/s3.service.spec.ts
 PASS  src/email/email.integration.spec.ts
 PASS  src/webhooks/dto/gitea-webhook.dto.spec.ts
 PASS  src/users/dto/users-dtos-part3.spec.ts
 PASS  src/users/dto/users-dtos-part2.spec.ts
 PASS  src/common/dto/common-dtos-part2.spec.ts
 PASS  src/common/guards/roles.guard.spec.ts
 PASS  src/admin/dto/admin-dtos.spec.ts
 PASS  src/developer/dto/index.spec.ts
 PASS  src/admin/admin.module.spec.ts
 PASS  src/admin/roles.service.spec.ts
 PASS  src/auth/dto/token-response.dto.spec.ts
 PASS  src/users/users.module.spec.ts
 PASS  src/common/interceptors/transform.interceptor.spec.ts
 PASS  src/uploads/uploads.module.spec.ts
 PASS  src/auth/dto/index.spec.ts
 PASS  src/common/filters/global-exception.filter.spec.ts
 PASS  src/common/interceptors/logging.interceptor.spec.ts
 PASS  src/webhooks/webhooks.module.spec.ts
 PASS  src/developer/developer.module.spec.ts
 PASS  src/client/client.module.spec.ts
 PASS  src/common/filters/all-exceptions.filter.spec.ts
 PASS  src/config/database.config.spec.ts
 PASS  src/common/guards/throttler.guard.spec.ts
 PASS  src/common/decorators/roles.decorator.spec.ts
 PASS  src/config/throttler.config.spec.ts
 PASS  src/config/jwt.config.spec.ts
 PASS  src/database/schema/marketplace-items.schema.spec.ts
 PASS  src/database/schema/user-roles.schema.spec.ts
 PASS  src/database/schema/role-permissions.schema.spec.ts
 PASS  src/database/schema/roles.schema.spec.ts
 PASS  src/common/utils.spec.ts
 FAIL  src/users/users.service.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      users/users.service.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.<anonymous> (users/users.service.integration.spec.ts:19:1)

 FAIL  src/users/users.controller.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      users/users.controller.ts
      users/users.controller.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.require (users/users.controller.ts:16:1)
      at Object.<anonymous> (users/users.controller.integration.spec.ts:16:1)

 PASS  src/email/email.service.spec.ts
 PASS  src/email/email.module.spec.ts
 FAIL  src/auth/auth.controller.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      auth/auth.controller.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.<anonymous> (auth/auth.controller.integration.spec.ts:15:1)

 FAIL  src/admin/admin.controller.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      admin/admin.controller.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.<anonymous> (admin/admin.controller.integration.spec.ts:20:1)

 FAIL  src/admin/admin.controller.authorization.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      admin/admin.controller.ts
      admin/admin.controller.authorization.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.require (admin/admin.controller.ts:7:1)
      at Object.<anonymous> (admin/admin.controller.authorization.spec.ts:8:1)

--------------------------------------------------|---------|----------|---------|---------|-------------------------------------
File                                              | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                   
--------------------------------------------------|---------|----------|---------|---------|-------------------------------------
All files                                         |   79.59 |    63.29 |   64.16 |   80.12 |                                     
 src                                              |     100 |      100 |     100 |     100 |                                     
  app.module.ts                                   |     100 |      100 |     100 |     100 |                                     
 src/admin                                        |    20.1 |    17.07 |   10.25 |   18.62 |                                     
  admin.controller.ts                             |       0 |        0 |       0 |       0 | 1-218                               
  admin.module.ts                                 |     100 |      100 |     100 |     100 |                                     
  permissions.service.ts                          |       0 |        0 |       0 |       0 | 1-91                                
  roles.service.ts                                |   59.18 |       70 |   44.44 |   55.55 | 14-28,42-77,94-95,110               
 src/admin/dto                                    |     100 |      100 |     100 |     100 |                                     
  admin-developer-view.dto.ts                     |     100 |      100 |     100 |     100 |                                     
  admin-update-user-request.dto.ts                |     100 |      100 |     100 |     100 |                                     
  admin-user-view.dto.ts                          |     100 |      100 |     100 |     100 |                                     
 src/auth                                         |   95.13 |    90.56 |   66.66 |   94.92 |                                     
  auth.controller.ts                              |      75 |      100 |   14.28 |   72.72 | 27,36,44,51,58,65                   
  auth.module.ts                                  |     100 |      100 |     100 |     100 |                                     
  auth.service.ts                                 |   99.05 |    90.56 |     100 |   99.03 | 330                                 
 src/auth/dto                                     |     100 |      100 |     100 |     100 |                                     
  auth-response.dto.ts                            |     100 |      100 |     100 |     100 |                                     
  index.ts                                        |     100 |      100 |     100 |     100 |                                     
  login-request.dto.ts                            |     100 |      100 |     100 |     100 |                                     
  register-request.dto.ts                         |     100 |      100 |     100 |     100 |                                     
  token-response.dto.ts                           |     100 |      100 |     100 |     100 |                                     
 src/auth/strategies                              |   91.66 |      100 |      50 |      90 |                                     
  jwt.strategy.ts                                 |   91.66 |      100 |      50 |      90 | 36                                  
 src/client                                       |   92.59 |      100 |     100 |    91.3 |                                     
  client.controller.ts                            |   90.47 |      100 |     100 |   89.47 | 126-127                             
  client.module.ts                                |     100 |      100 |     100 |     100 |                                     
 src/common                                       |     100 |      100 |     100 |     100 |                                     
  common.module.ts                                |     100 |      100 |     100 |     100 |                                     
 src/common/decorators                            |     100 |      100 |     100 |     100 |                                     
  current-user.decorator.ts                       |     100 |      100 |     100 |     100 |                                     
  public.decorator.ts                             |     100 |      100 |     100 |     100 |                                     
  roles.decorator.ts                              |     100 |      100 |     100 |     100 |                                     
 src/common/dto                                   |     100 |      100 |     100 |     100 |                                     
  create-repository-options.dto.ts                |     100 |      100 |     100 |     100 |                                     
  create-user-options.dto.ts                      |     100 |      100 |     100 |     100 |                                     
  gitea-response.dto.ts                           |     100 |      100 |     100 |     100 |                                     
  gitea-user.dto.ts                               |     100 |      100 |     100 |     100 |                                     
  update-user-options.dto.ts                      |     100 |      100 |     100 |     100 |                                     
  webhook-options.dto.ts                          |     100 |      100 |     100 |     100 |                                     
 src/common/enums                                 |     100 |      100 |     100 |     100 |                                     
  user-role.enum.ts                               |     100 |      100 |     100 |     100 |                                     
 src/common/filters                               |     100 |    97.56 |     100 |     100 |                                     
  all-exceptions.filter.ts                        |     100 |    92.85 |     100 |     100 | 32                                  
  global-exception.filter.ts                      |     100 |      100 |     100 |     100 |                                     
 src/common/guards                                |   83.33 |    53.84 |      75 |      80 |                                     
  jwt-auth.guard.ts                               |   56.25 |        0 |   33.33 |      50 | 14-30                               
  roles.guard.ts                                  |     100 |      100 |     100 |     100 |                                     
  throttler.guard.ts                              |     100 |      100 |     100 |     100 |                                     
 src/common/interceptors                          |     100 |      100 |     100 |     100 |                                     
  logging.interceptor.ts                          |     100 |      100 |     100 |     100 |                                     
  transform.interceptor.ts                        |     100 |      100 |     100 |     100 |                                     
 src/common/services                              |   96.87 |    88.96 |   98.09 |   96.84 |                                     
  client-releases.service.ts                      |    93.4 |      100 |     100 |   92.59 | 161-162,188-189,217-218             
  database.service.ts                             |     100 |      100 |     100 |     100 |                                     
  developer-management.service.ts                 |     100 |     91.3 |     100 |     100 | 35,64-115,214                       
  file-uploads.service.ts                         |   91.22 |    78.12 |     100 |   91.81 | 176-177,195,308-309,348,360,366-367 
  gitea.service.ts                                |   99.09 |    89.24 |   94.11 |   99.06 | 42                                  
  index.ts                                        |     100 |      100 |     100 |     100 |                                     
  redis.service.ts                                |     100 |    92.85 |     100 |     100 | 148                                 
  repository-sync.service.ts                      |     100 |     86.2 |     100 |     100 | 132-153                             
  s3.service.ts                                   |   97.64 |    92.85 |     100 |   97.59 | 171-172                             
  services.module.ts                              |     100 |      100 |     100 |     100 |                                     
  webhook-processor.service.ts                    |    93.9 |    86.36 |    92.3 |   93.67 | 210,225-226,271-272                 
 src/common/utils                                 |   70.49 |    60.97 |   61.53 |   69.49 |                                     
  secure-logger.ts                                |   70.49 |    60.97 |   61.53 |   69.49 | 88-150                              
 src/config                                       |     100 |      100 |     100 |     100 |                                     
  database.config.ts                              |     100 |      100 |     100 |     100 |                                     
  jwt.config.ts                                   |     100 |      100 |     100 |     100 |                                     
  throttler.config.ts                             |     100 |      100 |     100 |     100 |                                     
 src/database                                     |     100 |      100 |     100 |     100 |                                     
  database.module.ts                              |     100 |      100 |     100 |     100 |                                     
 src/database/schema                              |   86.29 |      100 |   56.41 |   85.47 |                                     
  client-releases.schema.ts                       |     100 |      100 |     100 |     100 |                                     
  devices.schema.ts                               |   77.77 |      100 |      50 |      75 | 7-22                                
  file-uploads.schema.ts                          |   81.81 |      100 |      50 |      80 | 13-56                               
  gitea-profiles.schema.ts                        |      90 |      100 |      75 |   88.88 | 7                                   
  gitea-repositories.schema.ts                    |   81.81 |      100 |      50 |      80 | 13-86                               
  index.ts                                        |     100 |      100 |     100 |     100 |                                     
  marketplace-items.schema.ts                     |      80 |      100 |      50 |   78.57 | 73-140                              
  permissions.schema.ts                           |     100 |      100 |     100 |     100 |                                     
  refresh-tokens.schema.ts                        |   77.77 |      100 |      50 |      75 | 8-16                                
  role-permissions.schema.ts                      |   66.66 |      100 |       0 |   66.66 | 7-8                                 
  roles.schema.ts                                 |     100 |      100 |     100 |     100 |                                     
  user-roles.schema.ts                            |   66.66 |      100 |       0 |   66.66 | 7-8                                 
  user-sessions.schema.ts                         |      90 |      100 |      75 |   88.88 | 10                                  
  users.schema.ts                                 |     100 |      100 |     100 |     100 |                                     
 src/developer                                    |   98.63 |    58.74 |     100 |   98.52 |                                     
  developer.controller.ts                         |   98.52 |    58.74 |     100 |   98.46 | 203                                 
  developer.module.ts                             |     100 |      100 |     100 |     100 |                                     
 src/developer/dto                                |     100 |      100 |   33.33 |     100 |                                     
  create-developer-request.dto.ts                 |     100 |      100 |     100 |     100 |                                     
  developer-analytics.dto.ts                      |     100 |      100 |     100 |     100 |                                     
  developer-payout-settings.dto.ts                |     100 |      100 |     100 |     100 |                                     
  developer-payout.dto.ts                         |     100 |      100 |     100 |     100 |                                     
  developer-repository.dto.ts                     |     100 |      100 |     100 |     100 |                                     
  gitea-profile.dto.ts                            |     100 |      100 |     100 |     100 |                                     
  gitea-webhook-payload.dto.ts                    |     100 |      100 |     100 |     100 |                                     
  index.ts                                        |     100 |      100 |       0 |     100 |                                     
  marketplace-metadata.dto.ts                     |     100 |      100 |     100 |     100 |                                     
  payout-request.dto.ts                           |     100 |      100 |     100 |     100 |                                     
  payout-settings.dto.ts                          |     100 |      100 |     100 |     100 |                                     
  provision-gitea-request.dto.ts                  |     100 |      100 |     100 |     100 |                                     
  publish-repository-request.dto.ts               |     100 |      100 |     100 |     100 |                                     
  repository-analytics.dto.ts                     |     100 |      100 |     100 |     100 |                                     
  repository-sync-response.dto.ts                 |     100 |      100 |     100 |     100 |                                     
  role-payout-settings.dto.ts                     |     100 |      100 |     100 |     100 |                                     
  update-developer-payout-settings-request.dto.ts |     100 |      100 |     100 |     100 |                                     
  update-payout-settings-request.dto.ts           |     100 |      100 |     100 |     100 |                                     
  update-role-payout-settings-request.dto.ts      |     100 |      100 |     100 |     100 |                                     
 src/email                                        |   89.13 |    66.66 |      75 |   88.09 |                                     
  email.module.ts                                 |   71.42 |        0 |      25 |   66.66 | 35-48                               
  email.service.ts                                |   96.87 |    85.71 |   91.66 |   96.66 | 157                                 
 src/store/dto                                    |     100 |      100 |     100 |     100 |                                     
  add-to-cart-request.dto.ts                      |     100 |      100 |     100 |     100 |                                     
  cart-item.dto.ts                                |     100 |      100 |     100 |     100 |                                     
  cart.dto.ts                                     |     100 |      100 |     100 |     100 |                                     
  instance-pricing-tier.dto.ts                    |     100 |      100 |     100 |     100 |                                     
  pricing-option.dto.ts                           |     100 |      100 |     100 |     100 |                                     
  pricing-response.dto.ts                         |     100 |      100 |     100 |     100 |                                     
  pricing-structure.dto.ts                        |     100 |      100 |     100 |     100 |                                     
  repository-pricing.dto.ts                       |     100 |      100 |     100 |     100 |                                     
  store-filters.dto.ts                            |     100 |      100 |     100 |     100 |                                     
  store-item-detailed.dto.ts                      |     100 |      100 |     100 |     100 |                                     
  store-item.dto.ts                               |     100 |      100 |     100 |     100 |                                     
  subscription-plan.dto.ts                        |     100 |      100 |     100 |     100 |                                     
  update-cart-item-request.dto.ts                 |     100 |      100 |     100 |     100 |                                     
 src/uploads                                      |   89.13 |       50 |    87.5 |   90.24 |                                     
  uploads.controller.ts                           |   87.17 |       50 |    87.5 |   88.88 | 105-120                             
  uploads.module.ts                               |     100 |      100 |     100 |     100 |                                     
 src/users                                        |    2.47 |        0 |       0 |    1.87 |                                     
  users.controller.ts                             |       0 |        0 |       0 |       0 | 1-133                               
  users.module.ts                                 |     100 |      100 |     100 |     100 |                                     
  users.service.ts                                |       0 |        0 |       0 |       0 | 1-883                               
 src/users/dto                                    |   68.45 |      100 |     100 |   68.45 |                                     
  applied-coupon.dto.ts                           |     100 |      100 |     100 |     100 |                                     
  applied-discount.dto.ts                         |     100 |      100 |     100 |     100 |                                     
  backup-codes-response.dto.ts                    |     100 |      100 |     100 |     100 |                                     
  bot-session.dto.ts                              |     100 |      100 |     100 |     100 |                                     
  btcpay-settings.dto.ts                          |     100 |      100 |     100 |     100 |                                     
  create-device-request.dto.ts                    |       0 |      100 |     100 |       0 | 1-23                                
  create-permission-request.dto.ts                |     100 |      100 |     100 |     100 |                                     
  create-role-request.dto.ts                      |     100 |      100 |     100 |     100 |                                     
  desktop-registration-response.dto.ts            |     100 |      100 |     100 |     100 |                                     
  desktop-session.dto.ts                          |     100 |      100 |     100 |     100 |                                     
  device.dto.ts                                   |       0 |      100 |     100 |       0 | 1-50                                
  error.dto.ts                                    |     100 |      100 |     100 |     100 |                                     
  feature-access-check.dto.ts                     |     100 |      100 |     100 |     100 |                                     
  item-performance.dto.ts                         |     100 |      100 |     100 |     100 |                                     
  marketplace-item.dto.ts                         |     100 |      100 |     100 |     100 |                                     
  order-item.dto.ts                               |     100 |      100 |     100 |     100 |                                     
  pagination.dto.ts                               |     100 |      100 |     100 |     100 |                                     
  permission.dto.ts                               |     100 |      100 |     100 |     100 |                                     
  revenue-summary.dto.ts                          |     100 |      100 |     100 |     100 |                                     
  role.dto.ts                                     |       0 |      100 |     100 |       0 | 1-43                                
  session-limits.dto.ts                           |     100 |      100 |     100 |     100 |                                     
  session.dto.ts                                  |       0 |      100 |     100 |       0 | 1-65                                
  two-factor-disable-request.dto.ts               |       0 |      100 |     100 |       0 | 1-13                                
  two-factor-enabled-response.dto.ts              |       0 |      100 |     100 |       0 | 1-18                                
  two-factor-setup-response.dto.ts                |       0 |      100 |     100 |       0 | 1-28                                
  two-factor-verify-request.dto.ts                |       0 |      100 |     100 |       0 | 1-8                                 
  update-permission-request.dto.ts                |     100 |      100 |     100 |     100 |                                     
  update-role-request.dto.ts                      |     100 |      100 |     100 |     100 |                                     
  update-user-request.dto.ts                      |       0 |      100 |     100 |       0 | 1-18                                
  upgrade-option.dto.ts                           |     100 |      100 |     100 |     100 |                                     
  user.dto.ts                                     |       0 |      100 |     100 |       0 | 1-69                                
  volume-discount.dto.ts                          |     100 |      100 |     100 |     100 |                                     
  web-session.dto.ts                              |     100 |      100 |     100 |     100 |                                     
 src/webhooks                                     |   95.12 |    81.81 |     100 |   94.59 |                                     
  webhooks.controller.ts                          |   94.44 |    81.81 |     100 |   94.11 | 82-83                               
  webhooks.module.ts                              |     100 |      100 |     100 |     100 |                                     
 src/webhooks/dto                                 |     100 |      100 |     100 |     100 |                                     
  gitea-webhook.dto.ts                            |     100 |      100 |     100 |     100 |                                     
  webhook-response.dto.ts                         |     100 |      100 |     100 |     100 |                                     
--------------------------------------------------|---------|----------|---------|---------|-------------------------------------

=============================== Coverage summary ===============================
Statements   : 79.59% ( 2380/2990 )
Branches     : 63.29% ( 519/820 )
Functions    : 64.16% ( 222/346 )
Lines        : 80.12% ( 2258/2818 )
================================================================================
Summary of all failing tests
 FAIL  users/users.service.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      users/users.service.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.<anonymous> (users/users.service.integration.spec.ts:19:1)

 FAIL  users/users.controller.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      users/users.controller.ts
      users/users.controller.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.require (users/users.controller.ts:16:1)
      at Object.<anonymous> (users/users.controller.integration.spec.ts:16:1)

 FAIL  auth/auth.controller.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      auth/auth.controller.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.<anonymous> (auth/auth.controller.integration.spec.ts:15:1)

 FAIL  admin/admin.controller.integration.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      admin/admin.controller.integration.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.<anonymous> (admin/admin.controller.integration.spec.ts:20:1)

 FAIL  admin/admin.controller.authorization.spec.ts
  ● Test suite failed to run

    Cannot find module 'src/email/email.service.js' from 'users/users.service.ts'

    Require stack:
      users/users.service.ts
      admin/admin.controller.ts
      admin/admin.controller.authorization.spec.ts

       8 | import * as qrcode from 'qrcode';
       9 | import * as speakeasy from 'speakeasy';
    > 10 | import { EmailService } from 'src/email/email.service.js';
         | ^
      11 | import { devices } from '../database/schema/devices.schema.js';
      12 | import { roles, userRoles, users } from '../database/schema/index.js';
      13 | import { userSessions } from '../database/schema/user-sessions.schema.js';

      at Resolver._throwModNotFoundError (../node_modules/.pnpm/jest-resolve@29.7.0/node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.require (users/users.service.ts:10:1)
      at Object.require (admin/admin.controller.ts:7:1)
      at Object.<anonymous> (admin/admin.controller.authorization.spec.ts:8:1)


Test Suites: 5 failed, 59 passed, 64 total
Tests:       1138 passed, 1138 total
Snapshots:   0 total
Time:        56.154 s

Jest has detected the following 1 open handle potentially keeping Jest from exiting:

  ●  CustomGC

      1 | import { MailerModule } from '@nestjs-modules/mailer';
    > 2 | import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
        | ^
      3 | import { Module } from '@nestjs/common';
      4 | import { ConfigModule, ConfigService } from '@nestjs/config';
      5 | import { join } from 'path';

      at Runtime._loadModule (../node_modules/.pnpm/jest-runtime@29.7.0/node_modules/jest-runtime/build/index.js:1018:29)
      at Object.<anonymous> (../node_modules/.pnpm/@css-inline+css-inline@0.14.1/node_modules/@css-inline/css-inline/index.js:206:31)
      at Object.<anonymous> (../node_modules/.pnpm/@nestjs-modules+mailer@2.0.2_@nestjs+common@11.1.2_class-transformer@0.5.1_class-valida_e4eb60fb5dff79f7bc1f03196b9afb90/node_modules/@nestjs-modules/mailer/dist/adapters/handlebars.adapter.js:7:22)
      at Object.require (email/email.module.ts:2:1)
      at Object.require (auth/auth.module.ts:7:1)
      at Object.<anonymous> (auth/auth.module.spec.ts:9:1)

 ELIFECYCLE  Command failed with exit code 1.
/app $ 